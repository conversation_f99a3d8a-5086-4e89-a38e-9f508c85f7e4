import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { z } from "zod";
// Create server instance
const server = new McpServer({
    name: "qbraid-mcp-server",
    version: "0.0.1",
    capabilities: {
        resources: {},
        tools: {},
        prompts: {},
    },
});
// Whitelist of read-only API path prefixes
const ALLOWED_PATHS = [
    "/providers",
    "/providers/",
    "/public/verify-org/",
    "/orgs/get/",
    "/organizations/",
    "/device-access-requests",
    "/activity-logs",
    "/v2/quantum-devices",
    "/quantum-jobs/cost-estimate",
    "/quantum-jobs/result/",
    "/billing/products",
    "/billing/credits/get-user-credits",
];
server.tool("api-get", "Read-only GET against whitelisted endpoints at https://xyz.com/api", {
    path: z.string(),
    query: z.record(z.string()).optional(),
    authToken: z.string().optional(),
}, async ({ path, query, authToken }) => {
    const base = "https://xyz.com/api";
    const normalizedPath = path.startsWith("/") ? path : `/${path}`;
    if (!ALLOWED_PATHS.some((p) => normalizedPath.startsWith(p))) {
        return { content: [{ type: "text", text: "Disallowed path" }] };
    }
    const url = new URL(normalizedPath, base);
    if (query)
        for (const [k, v] of Object.entries(query))
            url.searchParams.set(k, v);
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const body = await res.text();
    const prefix = res.ok ? "OK" : `HTTP ${res.status}`;
    return { content: [{ type: "text", text: `${prefix}: ${body}` }] };
});
// Provider tools
server.tool("list_providers", "List all quantum computing providers", {
    authToken: z.string().optional(),
}, async ({ authToken }) => {
    const url = "https://xyz.com/api/providers";
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
server.tool("get_provider_ownership", "Get ownership information for a specific provider", {
    providerId: z.string(),
    authToken: z.string().optional(),
}, async ({ providerId, authToken }) => {
    const url = `https://xyz.com/api/providers/${providerId}/ownership`;
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
// Organization tools
server.tool("verify_org_invite", "Verify organization invitation (no auth required)", {
    id: z.string(),
    email: z.string().email(),
}, async ({ id, email }) => {
    const url = `https://xyz.com/api/public/verify-org/${id}?email=${encodeURIComponent(email)}`;
    const headers = { accept: "application/json" };
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
server.tool("list_user_orgs", "List organizations the user belongs to", {
    page: z.number().min(1).default(1),
    limit: z.number().min(1).max(100).default(20),
    authToken: z.string().optional(),
}, async ({ page, limit, authToken }) => {
    const url = `https://xyz.com/api/orgs/get/${page}/${limit}`;
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
server.tool("get_org_by_name", "Get organization details by name", {
    name: z.string(),
    authToken: z.string().optional(),
}, async ({ name, authToken }) => {
    const url = `https://xyz.com/api/orgs/get/${encodeURIComponent(name)}`;
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
server.tool("get_org_device_access", "Get device access for an organization", {
    orgId: z.string(),
    authToken: z.string().optional(),
}, async ({ orgId, authToken }) => {
    const url = `https://xyz.com/api/organizations/${orgId}/device-access`;
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
// Device Access Request tools
server.tool("list_device_access_requests", "List device access requests with optional filters", {
    status: z.string().optional(),
    organizationId: z.string().optional(),
    authToken: z.string().optional(),
}, async ({ status, organizationId, authToken }) => {
    const url = new URL("https://xyz.com/api/device-access-requests");
    if (status)
        url.searchParams.set("status", status);
    if (organizationId)
        url.searchParams.set("organizationId", organizationId);
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
server.tool("get_device_access_request", "Get details of a specific device access request", {
    requestId: z.string(),
    authToken: z.string().optional(),
}, async ({ requestId, authToken }) => {
    const url = `https://xyz.com/api/device-access-requests/${requestId}`;
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
// Activity Logs tools
server.tool("get_activity_logs", "Get activity logs with filtering and pagination", {
    organizationId: z.string().optional(),
    userId: z.string().optional(),
    action: z.string().optional(),
    resourceType: z.string().optional(),
    status: z.string().optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    page: z.number().min(1).default(1),
    resultsPerPage: z.number().min(1).max(100).default(20),
    authToken: z.string().optional(),
}, async ({ organizationId, userId, action, resourceType, status, startDate, endDate, page, resultsPerPage, authToken, }) => {
    const url = new URL("https://xyz.com/api/activity-logs");
    if (organizationId)
        url.searchParams.set("organizationId", organizationId);
    if (userId)
        url.searchParams.set("userId", userId);
    if (action)
        url.searchParams.set("action", action);
    if (resourceType)
        url.searchParams.set("resourceType", resourceType);
    if (status)
        url.searchParams.set("status", status);
    if (startDate)
        url.searchParams.set("startDate", startDate);
    if (endDate)
        url.searchParams.set("endDate", endDate);
    url.searchParams.set("page", page.toString());
    url.searchParams.set("resultsPerPage", resultsPerPage.toString());
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
// Quantum Devices tools
server.tool("list_devices", "List quantum devices with filtering", {
    provider: z.string().optional(),
    type: z.string().optional(),
    status: z.string().optional(),
    isAvailable: z.boolean().optional(),
    authToken: z.string().optional(),
}, async ({ provider, type, status, isAvailable, authToken }) => {
    const url = new URL("https://xyz.com/api/v2/quantum-devices");
    if (provider)
        url.searchParams.set("provider", provider);
    if (type)
        url.searchParams.set("type", type);
    if (status)
        url.searchParams.set("status", status);
    if (isAvailable !== undefined)
        url.searchParams.set("isAvailable", isAvailable.toString());
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
server.tool("list_all_devices", "List all quantum devices across all providers", {
    authToken: z.string().optional(),
}, async ({ authToken }) => {
    const url = "https://xyz.com/api/v2/quantum-devices/all";
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
server.tool("get_provider_info", "Get provider information for quantum devices", {
    provider: z.string(),
    authToken: z.string().optional(),
}, async ({ provider, authToken }) => {
    const url = `https://xyz.com/api/v2/quantum-devices/providers/${provider}`;
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
// Quantum Jobs tools
server.tool("estimate_job_cost", "Estimate the cost of a quantum job", {
    qbraidDeviceId: z.string(),
    shots: z.number().min(1),
    minutes: z.number().min(1),
    authToken: z.string().optional(),
}, async ({ qbraidDeviceId, shots, minutes, authToken }) => {
    const url = new URL("https://xyz.com/api/quantum-jobs/cost-estimate");
    url.searchParams.set("qbraidDeviceId", qbraidDeviceId);
    url.searchParams.set("shots", shots.toString());
    url.searchParams.set("minutes", minutes.toString());
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
server.tool("get_job_result", "Get the result of a completed quantum job", {
    qbraidJobId: z.string(),
    authToken: z.string().optional(),
}, async ({ qbraidJobId, authToken }) => {
    const url = `https://xyz.com/api/quantum-jobs/result/${qbraidJobId}`;
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
// Billing tools
server.tool("list_billing_products", "List available billing products", {
    authToken: z.string().optional(),
}, async ({ authToken }) => {
    const url = "https://xyz.com/api/billing/products";
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
server.tool("get_user_credits", "Get user's credit balance", {
    authToken: z.string().optional(),
}, async ({ authToken }) => {
    const url = "https://xyz.com/api/billing/credits/get-user-credits";
    const headers = { accept: "application/json" };
    if (authToken)
        headers.authorization = `Bearer ${authToken}`;
    const res = await fetch(url, { headers });
    const data = await res.json();
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(data, null, 2),
            },
        ],
    };
});
function main() {
    const transport = new StreamableHTTPServerTransport({
        sessionIdGenerator: undefined,
    });
    server.connect(transport);
}
main();
